import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/services/firebase/firestore_service.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

/// Provider for the Firestore service
final firestoreServiceProvider = Provider<FirestoreService>((ref) {
  return FirestoreService();
});

/// Provider for the current custom system prompt
final customSystemPromptProvider = StreamProvider<String?>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return firestoreService.listenToCustomSystemPrompt();
});

/// Provider for the default system prompt from remote config
final defaultSystemPromptProvider = Provider<String>((ref) {
  final llmPrompts = ref.watch(llmPromptsProvider);
  return llmPrompts.getChatSystemInstruction();
});

/// State notifier for managing the system prompt editing state
class SystemPromptEditNotifier extends StateNotifier<SystemPromptEditState> {
  SystemPromptEditNotifier(this._firestoreService, String initialPrompt)
      : super(SystemPromptEditState(
          currentPrompt: initialPrompt,
          originalPrompt: initialPrompt,
          hasChanges: false,
          isSaving: false,
        ));

  final FirestoreService _firestoreService;

  void updatePrompt(String newPrompt) {
    state = state.copyWith(
      currentPrompt: newPrompt,
      hasChanges: newPrompt != state.originalPrompt,
    );
  }

  void restoreToDefault(String defaultPrompt) {
    state = state.copyWith(
      currentPrompt: defaultPrompt,
      hasChanges: defaultPrompt != state.originalPrompt,
    );
  }

  Future<bool> savePrompt() async {
    if (!state.hasChanges) return true;

    state = state.copyWith(isSaving: true);

    try {
      final success = await _firestoreService.saveCustomSystemPrompt(state.currentPrompt);
      if (success) {
        state = state.copyWith(
          originalPrompt: state.currentPrompt,
          hasChanges: false,
          isSaving: false,
        );
      } else {
        state = state.copyWith(isSaving: false);
      }
      return success;
    } catch (e) {
      Logger.error('Error saving custom system prompt', e);
      state = state.copyWith(isSaving: false);
      return false;
    }
  }

  bool isCurrentPromptSameAsDefault(String defaultPrompt) {
    return state.currentPrompt == defaultPrompt;
  }
}

/// State class for system prompt editing
class SystemPromptEditState {
  final String currentPrompt;
  final String originalPrompt;
  final bool hasChanges;
  final bool isSaving;

  const SystemPromptEditState({
    required this.currentPrompt,
    required this.originalPrompt,
    required this.hasChanges,
    required this.isSaving,
  });

  SystemPromptEditState copyWith({
    String? currentPrompt,
    String? originalPrompt,
    bool? hasChanges,
    bool? isSaving,
  }) {
    return SystemPromptEditState(
      currentPrompt: currentPrompt ?? this.currentPrompt,
      originalPrompt: originalPrompt ?? this.originalPrompt,
      hasChanges: hasChanges ?? this.hasChanges,
      isSaving: isSaving ?? this.isSaving,
    );
  }
}

/// Provider for the system prompt edit notifier
final systemPromptEditProvider = StateNotifierProvider.family<SystemPromptEditNotifier, SystemPromptEditState, String>(
  (ref, initialPrompt) {
    final firestoreService = ref.watch(firestoreServiceProvider);
    return SystemPromptEditNotifier(firestoreService, initialPrompt);
  },
);

/// Screen for customizing the system prompt
class CustomizeSystemPromptScreen extends ConsumerStatefulWidget {
  const CustomizeSystemPromptScreen({super.key});

  @override
  ConsumerState<CustomizeSystemPromptScreen> createState() => _CustomizeSystemPromptScreenState();
}

class _CustomizeSystemPromptScreenState extends ConsumerState<CustomizeSystemPromptScreen> {
  late TextEditingController _textController;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<bool> _showUnsavedChangesDialog() async {
    final result = await showDialog<String>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Unsaved Changes',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'You have unsaved changes. What would you like to do?',
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop('discard'),
              child: Text(
                'Discard',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop('save'),
              child: Text(
                'Save',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (result == 'save') {
      // Try to save and return whether we should close
      final editNotifier = ref.read(systemPromptEditProvider(_getInitialPrompt()).notifier);
      final saveSuccess = await editNotifier.savePrompt();
      return saveSuccess;
    } else if (result == 'discard') {
      return true;
    }
    return false; // User cancelled
  }

  String _getInitialPrompt() {
    final customPromptAsync = ref.read(customSystemPromptProvider);
    final defaultPrompt = ref.read(defaultSystemPromptProvider);
    
    return customPromptAsync.when(
      data: (customPrompt) => customPrompt ?? defaultPrompt,
      loading: () => defaultPrompt,
      error: (_, __) => defaultPrompt,
    );
  }

  @override
  Widget build(BuildContext context) {
    final customPromptAsync = ref.watch(customSystemPromptProvider);
    final defaultPrompt = ref.watch(defaultSystemPromptProvider);

    return customPromptAsync.when(
      data: (customPrompt) {
        final initialPrompt = customPrompt ?? defaultPrompt;
        final editState = ref.watch(systemPromptEditProvider(initialPrompt));
        final editNotifier = ref.read(systemPromptEditProvider(initialPrompt).notifier);

        // Update text controller if needed
        if (_textController.text != editState.currentPrompt) {
          _textController.text = editState.currentPrompt;
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Customize System Prompt',
              style: GoogleFonts.afacad(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: NoejiTheme.colorsOf(context).textPrimary,
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () async {
                if (editState.hasChanges) {
                  final navigator = Navigator.of(context);
                  final shouldClose = await _showUnsavedChangesDialog();
                  if (shouldClose && mounted) {
                    navigator.pop();
                  }
                } else {
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Text field for editing
                Expanded(
                  child: TextField(
                    controller: _textController,
                    focusNode: _focusNode,
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                    decoration: InputDecoration(
                      hintText: 'Enter your custom system prompt...',
                      hintStyle: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
                        color: NoejiTheme.colorsOf(context).textSecondary,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.zero,
                        borderSide: BorderSide(
                          color: NoejiTheme.colorsOf(context).border,
                          width: 1,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.zero,
                        borderSide: BorderSide(
                          color: NoejiTheme.colorsOf(context).border,
                          width: 1,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.zero,
                        borderSide: BorderSide(
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          width: 1,
                        ),
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                    onChanged: (value) {
                      editNotifier.updatePrompt(value);
                    },
                  ),
                ),

                const SizedBox(height: 24),

                // Buttons row
                Row(
                  children: [
                    // Restore to default button
                    Expanded(
                      child: TextButton(
                        onPressed: editNotifier.isCurrentPromptSameAsDefault(defaultPrompt)
                            ? null
                            : () async {
                                final shouldRestore = await _showRestoreConfirmationDialog();
                                if (shouldRestore) {
                                  editNotifier.restoreToDefault(defaultPrompt);
                                }
                              },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: BorderSide(
                            color: NoejiTheme.colorsOf(context).border,
                            width: 1,
                          ),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.zero,
                          ),
                        ),
                        child: Text(
                          'Restore to Default',
                          style: NoejiTheme.textStylesOf(context).buttonText.copyWith(
                            color: editNotifier.isCurrentPromptSameAsDefault(defaultPrompt)
                                ? NoejiTheme.colorsOf(context).textSecondary
                                : NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Save button
                    Expanded(
                      child: TextButton(
                        onPressed: (!editState.hasChanges || editState.isSaving)
                            ? null
                            : () async {
                                final scaffoldMessenger = ScaffoldMessenger.of(context);
                                final shouldSave = await _showSaveConfirmationDialog();
                                if (shouldSave) {
                                  final success = await editNotifier.savePrompt();
                                  if (success && mounted) {
                                    scaffoldMessenger.showSnackBar(
                                      const SnackBar(
                                        content: Text('System prompt saved successfully'),
                                      ),
                                    );
                                  } else if (mounted) {
                                    scaffoldMessenger.showSnackBar(
                                      const SnackBar(
                                        content: Text('Failed to save system prompt'),
                                      ),
                                    );
                                  }
                                }
                              },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: BorderSide(
                            color: NoejiTheme.colorsOf(context).border,
                            width: 1,
                          ),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.zero,
                          ),
                        ),
                        child: editState.isSaving
                            ? const SizedBox(
                                height: 16,
                                width: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : Text(
                                'Save',
                                style: NoejiTheme.textStylesOf(context).buttonText.copyWith(
                                  color: (!editState.hasChanges || editState.isSaving)
                                      ? NoejiTheme.colorsOf(context).textSecondary
                                      : NoejiTheme.colorsOf(context).textPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
      loading: () => Scaffold(
        appBar: AppBar(
          title: Text(
            'Customize System Prompt',
            style: GoogleFonts.afacad(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stackTrace) => Scaffold(
        appBar: AppBar(
          title: Text(
            'Customize System Prompt',
            style: GoogleFonts.afacad(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),
        ),
        body: Center(
          child: Text(
            'Error loading system prompt: $error',
            style: NoejiTheme.textStylesOf(context).bodyMedium,
          ),
        ),
      ),
    );
  }

  Future<bool> _showSaveConfirmationDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Save System Prompt',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'Are you sure you want to save this custom system prompt?',
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Save',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  Future<bool> _showRestoreConfirmationDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Restore to Default',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'Are you sure you want to replace the current text with the default system prompt?',
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Restore',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }
}
